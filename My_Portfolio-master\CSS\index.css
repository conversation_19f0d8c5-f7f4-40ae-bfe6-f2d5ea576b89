/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background: #0f0f0f; /* Dark background */
    color: #fff; /* Light text color */
    line-height: 1.6;
    overflow-x: hidden; /* Prevent horizontal scroll */
    margin-left: 70px; /* Account for compact sidebar width */
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #0a192f;
}

::-webkit-scrollbar-thumb {
    background: #64ffda;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #52d1b2;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
}

h1, h2, h3 {
    margin: 0;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 70px;
    height: 100vh;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 0;
    z-index: 1000;
    /* Rounded corners on the right side */
    border-radius: 0 15px 15px 0;
    /* Enhanced glow effect */
    box-shadow:
        2px 0 10px rgba(0, 0, 0, 0.3),
        2px 0 20px rgba(100, 255, 218, 0.08),
        4px 0 40px rgba(100, 255, 218, 0.04);
    border-right: 1px solid rgba(100, 255, 218, 0.15);
}

/* Add subtle glowing backdrop effect with rounded corners */
.sidebar::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 0;
    width: 86px;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(100, 255, 218, 0.03) 0%,
        rgba(100, 255, 218, 0.01) 50%,
        transparent 100%
    );
    border-radius: 0 15px 15px 0;
    pointer-events: none;
    z-index: -1;
}

.sidebar-header {
    margin-bottom: 18px;
}

.sidebar-header .logo {
    width: 38px;
    height: 38px;
    background: linear-gradient(135deg, #64ffda, #52d1b2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #0a192f;
    font-weight: 600;
    font-style: italic;
    letter-spacing: -0.5px;
    font-family: 'Dancing Script', 'Brush Script MT', cursive;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: rotate(-5deg);
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.sidebar-menu li {
    position: relative;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    color: #8892b0;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
    font-size: 16px;
}

.sidebar-menu a:hover {
    background: rgba(100, 255, 218, 0.1);
    color: #64ffda;
    transform: translateX(5px);
}

.sidebar-menu a.active {
    background: rgba(100, 255, 218, 0.2);
    color: #64ffda;
}

.sidebar-menu a::before {
    content: attr(title);
    position: absolute;
    left: 60px;
    background: linear-gradient(135deg, #0f0f0f, #1a1a1a);
    color: #64ffda;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(100, 255, 218, 0.3);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(100, 255, 218, 0.1);
    z-index: 1001;
}

.sidebar-menu a:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.sidebar-footer {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 5px;
    margin-bottom: 8px;
}

.sidebar-footer a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    color: #8892b0;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 15px;
}

.sidebar-footer a:hover {
    background: rgba(100, 255, 218, 0.1);
    color: #64ffda;
    transform: scale(1.1);
}

/* Reveal animation for sections */
.reveal {
    position: relative;
    opacity: 0;
    transition: all 1s ease;
}

.reveal.active {
    opacity: 1;
}

.reveal.fade-bottom {
    transform: translateY(50px);
}

.reveal.fade-bottom.active {
    transform: translateY(0);
}

.reveal.fade-left {
    transform: translateX(-100px);
}

.reveal.fade-left.active {
    transform: translateX(0);
}

.reveal.fade-right {
    transform: translateX(100px);
}

.reveal.fade-right.active {
    transform: translateX(0);
}

/* Hero Section */
#hero {
    background: #0f0f0f; /* Match body background */
    color: #fff;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Global Animated Background */
.animated-background-global {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

#networkCanvas {
    width: 100%;
    height: 100%;
    display: block;
    opacity: 1; /* Full opacity for maximum visibility */
}

/* Ensure hero content stays above animated background */
#hero h1,
#hero .hero-subtitle,
#hero .cta-button {
    position: relative;
    z-index: 3;
}

/* Add subtle backdrop filter for better text readability */
#hero .container {
    backdrop-filter: blur(0.5px);
    border-radius: 10px;
    padding: 40px;
}

/* Ensure all sections appear above the global animated background */
section {
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.4) 0%,
        rgba(26, 26, 46, 0.35) 50%,
        rgba(15, 15, 15, 0.4) 100%); /* Lighter gradient to show animation */
    backdrop-filter: blur(2px) saturate(1.1); /* Reduced blur for visibility */
    border: 1px solid rgba(100, 255, 218, 0.15); /* Slightly more visible border */
    margin: 10px 0; /* Small margin between sections */
    overflow: hidden; /* Ensure clean edges */
}

/* Hero section should be fully transparent to show the animation */
#hero {
    background: transparent;
    backdrop-filter: none;
    border: none;
    margin: 0;
}

/* Special styling for specific sections */
#about, #skills, #projects, #certifications, #cv-download, #contact {
    border-radius: 15px; /* Rounded corners for modern look */
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); /* Lighter shadow */
    transition: all 0.3s ease;
}

/* Hover effect for sections - enhance background slightly on hover */
#about:hover, #skills:hover, #projects:hover, #certifications:hover, #cv-download:hover, #contact:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.5) 0%,
        rgba(26, 26, 46, 0.45) 50%,
        rgba(15, 15, 15, 0.5) 100%); /* Slightly more opaque on hover */
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: rgba(100, 255, 218, 0.25);
}

/* Enhanced container styling for all sections */
section .container {
    position: relative;
    z-index: 2;
    padding: 40px 20px; /* Enhanced padding for blur effect */
}

/* Improve text readability against animated background */
section h1, section h2, section h3, section h4, section h5, section h6 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                 0 0 8px rgba(0, 0, 0, 0.6); /* Strong text shadow for headers */
}

section p, section li, section span {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7),
                 0 0 4px rgba(0, 0, 0, 0.5); /* Moderate text shadow for body text */
}

#hero .container {
    position: relative;
    z-index: 2;
}

#hero h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5em;
    margin-bottom: 20px;
    position: relative;
}

.hero-subtitle {
    font-size: 1.5em;
    color: #ddd;
    margin-bottom: 30px;
    height: 30px;
}

.typed-text {
    color: #ff4d00; /* Orange accent color */
    font-weight: 500;
}

.cursor {
    display: inline-block;
    width: 3px;
    background-color: #ff4d00; /* Orange accent color */
    margin-left: 5px;
    animation: blink 1s infinite;
    height: 1.5em;
    position: relative;
    top: 4px;
}

.cursor.typing {
    animation: none;
}

@keyframes blink {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

#hero h1 span.highlight {
    color: #ff4d00; /* Orange accent color */
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 77, 0, 0.5), 0 0 20px rgba(255, 77, 0, 0.3);
    position: relative;
    display: inline-block;
    animation: glowPulse 3s infinite;
}

@keyframes glowPulse {
    0%, 100% { text-shadow: 0 0 10px rgba(255, 77, 0, 0.5), 0 0 20px rgba(255, 77, 0, 0.3); }
    50% { text-shadow: 0 0 15px rgba(255, 77, 0, 0.8), 0 0 30px rgba(255, 77, 0, 0.5); }
}

.cta-button {
    display: inline-block;
    padding: 12px 30px;
    background: #ff4d00; /* Orange accent color */
    color: #fff;
    text-decoration: none;
    border-radius: 30px;
    font-size: 1.2em;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(255, 77, 0, 0.2);
    z-index: 1;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background: #ff7e40; /* Lighter orange */
    transition: all 0.4s cubic-bezier(0.42, 0, 0.58, 1);
    z-index: -1;
}

.cta-button:hover {
    color: #fff;
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(255, 77, 0, 0.4);
}

.cta-button:hover::before {
    width: 100%;
}
/* Divider Line */
.section-divider {
    border: 0;
    height: 1px;
    background: linear-gradient(90deg, #ff4d00, #333); /* Orange to dark gradient */
    margin: 0 auto; /* Center the line */
    width: 98%; /* Adjust width as needed */
    position: relative;
    z-index: 2;
}

/* Header Section */
header {
    background: violet; /* Dark blue with violet */
    color: #fff;
    padding: 60px 0;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    animation: fadeInDown 1s ease-in-out;
}

header p {
    font-size: 1.2em;
    color: #ddd;
    animation: fadeInUp 1s ease-in-out;
}

.social-icons {
    margin-top: 20px;
    animation: fadeIn 2s ease-in-out;
}

.social-icons a {
    color: #fff;
    font-size: 1.5em;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #64ffda; /* Teal color for hover */
}

/* About Section */
#about {
    padding: 80px 0;
    background: #151515; /* Slightly lighter than body */
    position: relative;
    overflow: hidden;
}

/* Tech pattern background */
#about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(30deg, rgba(100, 255, 218, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(100, 255, 218, 0.03) 87.5%, rgba(100, 255, 218, 0.03)),
        linear-gradient(150deg, rgba(100, 255, 218, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(100, 255, 218, 0.03) 87.5%, rgba(100, 255, 218, 0.03)),
        linear-gradient(30deg, rgba(100, 255, 218, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(100, 255, 218, 0.03) 87.5%, rgba(100, 255, 218, 0.03)),
        linear-gradient(150deg, rgba(100, 255, 218, 0.03) 12%, transparent 12.5%, transparent 87%, rgba(100, 255, 218, 0.03) 87.5%, rgba(100, 255, 218, 0.03));
    background-size: 80px 140px;
    opacity: 0.3;
    z-index: 0;
}

#about .container {
    position: relative;
    z-index: 1;
}

.about-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 60px;
}

.about-text {
    max-width: 800px; /* Wider text area */
    text-align: left;
}

.about-text h2 {
    font-size: 2.5em; /* Larger heading */
    color: #ff4d00; /* Orange accent color */
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: #ff4d00; /* Orange accent color */
    transition: width 0.5s ease;
}

.about-text:hover h2::after {
    width: 100%;
}

.about-text p {
    font-size: 1.5em; /* Larger paragraph text */
    color: #ddd; /* Light text color */
    line-height: 1.8; /* Better line spacing */
    margin-bottom: 20px; /* Add some space below the paragraph */
    position: relative;
    padding-left: 20px;
    border-left: 3px solid rgba(100, 255, 218, 0.3);
}

.profile-img {
    width: 280px; /* Larger image */
    height: auto;
    border-radius: 15px; /* Slightly rounded corners */
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.2); /* Teal shadow */
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
    border: 3px solid rgba(100, 255, 218, 0.3);
}

.profile-img::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    width: 100%;
    height: 100%;
    border: 3px solid #64ffda;
    border-radius: 15px;
    z-index: -1;
    transition: all 0.3s ease;
}

.profile-img:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.4);
}

.profile-img:hover::before {
    top: 10px;
    left: 10px;
}

/* Skills Section */
#skills {
    padding: 60px 0;
    background: #0f0f0f; /* Match body background */
    position: relative;
    overflow: hidden;
}

/* Tech pattern background */
#skills::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25px 25px, rgba(100, 255, 218, 0.1) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(100, 255, 218, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
    opacity: 0.5;
    z-index: 0;
}

#skills .container {
    position: relative;
    z-index: 1;
}

#skills h2 {
    font-size: 2.2em;
    color: #ff4d00; /* Orange accent color */
    margin-bottom: 30px;
    text-align: left;
    position: relative;
    display: inline-block;
}

.skills-category {
    margin-bottom: 50px;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.8s ease;
}

.skills-category.active {
    transform: translateY(0);
    opacity: 1;
}

.skills-category h3 {
    font-size: 1.6em;
    color: #fff;
    margin-bottom: 25px;
    text-align: left;
    position: relative;
    display: inline-block;
}

.skills-category h3::before {
    content: '{ ';
    color: #ff4d00; /* Orange accent color */
    margin-right: 5px;
}

.skills-category h3::after {
    content: ' }';
    color: #ff4d00; /* Orange accent color */
    margin-left: 5px;
}

/* Skills section now uses tech-badges for consistency */
.skills-category .tech-badges {
    margin-top: 20px;
}

/* Make skills section tech badges larger than project tech badges */
.skills-category .tech-badge {
    padding: 12px 20px;
    font-size: 1.1em;
    border-radius: 25px;
}

.skills-category .tech-badge i {
    margin-right: 8px;
    font-size: 1.2em;
}

/* Projects Section */
#projects {
    padding: 80px 0;
    background: #0f0f0f; /* Match body background */
    position: relative;
}

#projects .container {
    position: relative;
    z-index: 1;
    max-width: 1400px;
}

#projects h2 {
    font-size: 3em;
    color: #fff;
    margin-bottom: 10px;
    text-align: center;
    position: relative;
    font-weight: 700;
}

#projects h2 span {
    color: #ff4d00; /* Orange accent color */
}

#projects .subtitle {
    text-align: center;
    color: #aaa;
    font-size: 1.2em;
    margin-bottom: 60px;
}

.project-item {
    display: flex;
    margin-bottom: 100px;
    position: relative;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.project-item.active {
    opacity: 1;
    transform: translateY(0);
}

.project-item:nth-child(odd) {
    flex-direction: row;
}

.project-item:nth-child(even) {
    flex-direction: row-reverse;
}

.project-number {
    font-size: 5em;
    color: #ff4d00;
    font-weight: 700;
    position: absolute;
    top: -60px;
    left: 0;
    opacity: 0.2;
    z-index: 0;
}

.project-item:nth-child(even) .project-number {
    left: auto;
    right: 0;
}

.project-content {
    flex: 1;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.project-image {
    flex: 1.5;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    height: 300px; /* Fixed height for consistency */
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 20px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    transition: transform 0.5s ease;
}

.project-item:hover .project-image img {
    transform: scale(1.05);
}

/* Specific positioning for projects 1 and 2 - move images down */
.project-item:nth-child(3) .project-image,
.project-item:nth-child(4) .project-image {
    align-items: flex-start;
    padding-top: 80px;
    padding-bottom: 0;
}

.project-title {
    font-size: 2em;
    color: #fff;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.project-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #ff4d00; /* Orange accent color */
    transition: width 0.3s ease;
}

.project-item:hover .project-title::after {
    width: 100%;
}

.project-description {
    font-size: 1.1em;
    color: #ddd;
    margin-bottom: 25px;
    line-height: 1.6;
}

/* Tech Stack Styling */
.tech-stack {
    margin: 20px 0;
}

.tech-stack-label {
    font-size: 0.9em;
    color: #ff4d00;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tech-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tech-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    color: #fff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tech-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.tech-badge:hover::before {
    left: 100%;
}

.tech-badge i {
    margin-right: 6px;
    font-size: 1em;
}

/* Technology-specific colors */
.tech-badge.html {
    background: linear-gradient(135deg, #e34f26, #f06529);
}

.tech-badge.css {
    background: linear-gradient(135deg, #1572b6, #33a9dc);
}

.tech-badge.javascript {
    background: linear-gradient(135deg, #f7df1e, #f0db4f);
    color: #333;
}

.tech-badge.bootstrap {
    background: linear-gradient(135deg, #7952b3, #563d7c);
}

.tech-badge.php {
    background: linear-gradient(135deg, #777bb4, #8892bf);
}

.tech-badge.mysql {
    background: linear-gradient(135deg, #4479a1, #00758f);
}

.tech-badge.cpp {
    background: linear-gradient(135deg, #00599c, #004482);
}

.tech-badge.qt {
    background: linear-gradient(135deg, #41cd52, #00b04f);
}

.tech-badge.c {
    background: linear-gradient(135deg, #a8b9cc, #283593);
}

.tech-badge.python {
    background: linear-gradient(135deg, #3776ab, #ffd43b);
}

.tech-badge.vscode {
    background: linear-gradient(135deg, #007acc, #0e639c);
}

.tech-badge.mvc {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.tech-badge.matlab {
    background: linear-gradient(135deg, #e16737, #ff8c00);
}

.tech-badge.soft-skill {
    background: linear-gradient(135deg, #64ffda, #4ecdc4);
    color: #0a192f;
}

.project-actions {
    display: flex;
    gap: 15px;
}

@media (max-width: 768px) {
    .project-item,
    .project-item:nth-child(odd),
    .project-item:nth-child(even) {
        flex-direction: column;
    }

    .project-image {
        margin-bottom: 20px;
        min-height: 250px;
    }

    .project-number {
        top: -40px;
        font-size: 4em;
    }

    /* Tech stack responsive adjustments */
    .tech-badges {
        gap: 6px;
    }

    .tech-badge {
        padding: 5px 10px;
        font-size: 0.8em;
    }

    .tech-badge i {
        margin-right: 4px;
        font-size: 0.9em;
    }
}

/* Project link button */
.project-link {
  --width: 150px;
  --height: 40px;
  --tooltip-height: 35px;
  --tooltip-width: 120px;
  --gap-between-tooltip-to-button: 18px;
  --button-color: #ff4d00;
  --tooltip-color: #fff;
  width: var(--width);
  height: var(--height);
  background: var(--button-color);
  position: relative;
  text-align: center;
  border-radius: 0.45em;
  font-family: "Poppins", sans-serif;
  transition: background 0.3s;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  margin-top: 10px;
}

.project-link .text {
  line-height: var(--height);
  font-size: 0.95em;
  font-weight: 600;
  color: #fff;
}

.project-link .icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.project-link .icon i {
  font-size: 1.2em;
}

.project-link:hover {
  background: #ff7e40;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 77, 0, 0.3);
}

.project-link:hover .text {
  top: -100%;
}

.project-link:hover .icon {
  top: 0;
}

/* Set different tooltips for each project */
.project-link[data-tooltip="View on GitHub"] {
  --tooltip-width: 120px;
}

/* Certifications Section */
#certifications {
    padding: 60px 0;
    background: #0f0f0f; /* Match body background */
}

#certifications h2{
    font-size: 2em;
    color: #ff4d00; /* Orange accent color */
    margin-bottom: 20px;
}
.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.certification {
    background: #151515; /* Slightly lighter than body */
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.certification:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(255, 77, 0, 0.2); /* Orange shadow */
}

.certification img {
    max-width: 100%;
    border-radius: 10px;
    margin-bottom: 15px;
}

.certification h3 {
    margin: 10px 0;
    font-size: 1.2em;
    color: #fff; /* Light text color */
}

.certification p {
    font-size: 0.9em;
    color: #ddd; /* Light text color */
    margin: 5px 0;
}

/* Resume Section */
#resume {
    padding: 60px 0;
    background: #0a192f; /* Dark blue */
    text-align: center;
}

.resume-link {
    display: inline-block;
    padding: 10px 20px;
    background: #64ffda; /* Teal color */
    color: #0a192f; /* Dark text */
    text-decoration: none;
    border-radius: 5px;
    margin-top: 10px;
    transition: background 0.3s ease;
}

.resume-link:hover {
    background: #52d1b2; /* Darker teal on hover */
}

/* CV Download Section */
#cv-download {
    padding: 60px 0;
    background: #151515; /* Slightly lighter than body */
    text-align: center;
}

#cv-download h2 {
    font-size: 2.5em;
    color: #ff4d00; /* Orange accent color */
    margin-bottom: 20px;
}

#cv-download p {
    font-size: 1.2em;
    color: #ddd; /* Light text color */
    margin-bottom: 30px;
}

/* Fancy button with tooltip */
.button {
  --width: 180px;
  --height: 45px;
  --tooltip-height: 35px;
  --tooltip-width: 90px;
  --gap-between-tooltip-to-button: 18px;
  --button-color: #ff4d00;
  --tooltip-color: #fff;
  width: var(--width);
  height: var(--height);
  background: var(--button-color);
  position: relative;
  text-align: center;
  border-radius: 0.45em;
  font-family: "Poppins", sans-serif;
  transition: background 0.3s;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
}

.button::before {
  position: absolute;
  content: attr(data-tooltip);
  width: var(--tooltip-width);
  height: var(--tooltip-height);
  background-color: var(--tooltip-color);
  font-size: 0.9rem;
  color: #111;
  border-radius: .25em;
  line-height: var(--tooltip-height);
  bottom: calc(var(--height) + var(--gap-between-tooltip-to-button) + 10px);
  left: calc(50% - var(--tooltip-width) / 2);
}

.button::after {
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-top-color: var(--tooltip-color);
  left: calc(50% - 10px);
  bottom: calc(100% + var(--gap-between-tooltip-to-button) - 10px);
}

.button::after,.button::before {
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
}

.text {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-wrapper,.text,.icon {
  overflow: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  color: #fff;
}

.text {
  top: 0;
  line-height: var(--height);
  font-size: 1.1em;
  font-weight: 600;
}

.text,.icon {
  transition: top 0.5s;
}

.icon {
  color: #fff;
  top: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  width: 24px;
  height: 24px;
}

.button:hover {
  background: #ff7e40;
}

.button:hover .text {
  top: -100%;
}

.button:hover .icon {
  top: 0;
}

.button:hover:before,.button:hover:after {
  opacity: 1;
  visibility: visible;
}

.button:hover:after {
  bottom: calc(var(--height) + var(--gap-between-tooltip-to-button) - 20px);
}

.button:hover:before {
  bottom: calc(var(--height) + var(--gap-between-tooltip-to-button));
}

/* CV download button */
.cv-download-button {
  --width: 200px;
  margin-top: 20px;
}

/* Contact Section */
#contact {
    padding: 80px 0;
    background: #0f0f0f; /* Match body background */
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Animated background */
#contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(100, 255, 218, 0.05) 0%, transparent 10%),
        radial-gradient(circle at 80% 70%, rgba(100, 255, 218, 0.05) 0%, transparent 10%);
    animation: pulse 8s infinite alternate;
    z-index: 0;
}

#contact .container {
    position: relative;
    z-index: 1;
}

#contact h2 {
    font-size: 2.8em;
    color: #ffffff;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

#contact h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #ff4d00; /* Orange accent color */
}

.contact-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    max-width: 500px;
    margin: 0 auto;
    padding: 30px;
    background: rgba(10, 25, 47, 0.5);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(100, 255, 218, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-info:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.2);
}

#contact p {
    font-size: 1.3em;
    color: #ddd;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    transition: all 0.3s ease;
}

#contact p:hover {
    transform: translateY(-5px);
}

#contact i {
    margin-right: 15px;
    color: #ff4d00; /* Orange accent color */
    font-size: 1.5em;
    transition: all 0.3s ease;
}

#contact i:hover {
    transform: scale(1.2) rotate(15deg);
}

#contact .email, #contact .phone {
    color: #ff4d00; /* Orange accent color */
    position: relative;
    padding-bottom: 3px;
}

#contact .email::after, #contact .phone::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #ff4d00; /* Orange accent color */
    transition: width 0.3s ease;
}

#contact .email:hover::after, #contact .phone:hover::after {
    width: 100%;
}


/* Footer */
footer {
    background: #0a192f; /* Dark blue */
    color: #fff;
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
}

footer p {
    margin: 0;
    font-size: 0.9em;
}

footer .social-icons a {
    color: #fff;
    font-size: 1.2em;
    margin: 0 10px;
    transition: color 0.3s ease;
}

footer .social-icons a:hover {
    color: #64ffda; /* Teal color for hover */
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes codeRain {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 0% 100%;
    }
}

/* Media Queries for Responsiveness */
@media screen and (max-width: 768px) {
    /* Sidebar responsive adjustments */
    body {
        margin-left: 0; /* Remove sidebar margin on mobile */
    }

    .sidebar {
        width: 100%;
        height: 70px;
        flex-direction: row;
        justify-content: center;
        padding: 10px 0;
        bottom: 0;
        top: auto;
        border-right: none;
        border-top: 1px solid rgba(100, 255, 218, 0.15);
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
        /* Rounded top corners for mobile */
        border-radius: 15px 15px 0 0;
        /* Mobile glow effect */
        box-shadow:
            0 -2px 10px rgba(0, 0, 0, 0.3),
            0 -4px 20px rgba(100, 255, 218, 0.08),
            0 -8px 40px rgba(100, 255, 218, 0.04);
    }

    /* Mobile glowing backdrop effect with rounded corners */
    .sidebar::before {
        left: 0;
        top: -10px;
        width: 100%;
        height: 90px;
        background: linear-gradient(
            180deg,
            transparent 0%,
            rgba(100, 255, 218, 0.01) 50%,
            rgba(100, 255, 218, 0.03) 100%
        );
        border-radius: 15px 15px 0 0;
    }

    .sidebar-header {
        display: none; /* Hide logo on mobile */
    }

    .sidebar-menu {
        flex-direction: row;
        gap: 15px;
        flex: none;
    }

    .sidebar-menu a {
        width: 42px;
        height: 42px;
        font-size: 15px;
    }

    .sidebar-menu a:hover {
        transform: translateY(-5px);
    }

    .sidebar-menu a::before {
        left: 50%;
        bottom: 60px;
        transform: translateX(-50%);
    }

    .sidebar-menu a:hover::before {
        transform: translateX(-50%) translateY(0);
    }

    .sidebar-footer {
        display: none; /* Hide social links on mobile */
    }

    /* Add bottom padding to body to account for bottom sidebar */
    body {
        padding-bottom: 80px;
    }

    #hero h1 {
        font-size: 2.5em;
    }

    /* Reduce animated background intensity on mobile */
    .animated-background-global {
        opacity: 0.4;
    }

    #hero .container {
        padding: 20px;
    }

    /* Adjust blur effect for mobile */
    section {
        backdrop-filter: blur(1px); /* Minimal blur for mobile - show more animation */
        background: linear-gradient(135deg,
            rgba(15, 15, 15, 0.5) 0%,
            rgba(26, 26, 46, 0.45) 50%,
            rgba(15, 15, 15, 0.5) 100%); /* Slightly more opaque for mobile readability */
        margin: 5px 0;
        border-radius: 10px;
    }

    section .container {
        padding: 20px 15px; /* Reduced padding for mobile */
    }

    .about-content {
        flex-direction: column;
        text-align: center;
    }

    .about-text {
        margin-bottom: 30px;
    }

    .profile-img {
        margin: 0 auto;
    }

    .projects-grid, .certifications-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

